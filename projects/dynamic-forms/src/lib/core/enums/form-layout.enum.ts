export enum FormLayout {
  VERTICAL = 'vertical',
  HORIZONTAL = 'horizontal',
  INLINE = 'inline',
  GRID = 'grid',
  STEPPER = 'stepper',
  TABS = 'tabs',
  ACCORDION = 'accordion',
  WIZARD = 'wizard',
}

export enum FormMode {
  CREATE = 'create',
  EDIT = 'edit',
  VIEW = 'view',
  READONLY = 'readonly',
}

export enum FormState {
  IDLE = 'idle',
  LOADING = 'loading',
  VALIDATING = 'validating',
  SUBMITTING = 'submitting',
  SUCCESS = 'success',
  ERROR = 'error',
  CANCELLED = 'cancelled',
}

export enum FieldSize {
  EXTRA_SMALL = 'xs',
  SMALL = 'sm',
  MEDIUM = 'md',
  LARGE = 'lg',
  EXTRA_LARGE = 'xl',
  FULL = 'full',
}

export enum FieldAppearance {
  OUTLINE = 'outline',
  FILL = 'fill',
  STANDARD = 'standard',
  LEGACY = 'legacy',
}

export enum FieldLabelPosition {
  ABOVE = 'above',
  INLINE = 'inline',
  FLOATING = 'floating',
  NONE = 'none',
}

export enum ButtonType {
  SUBMIT = 'submit',
  CANCEL = 'cancel',
  RESET = 'reset',
  SAVE = 'save',
  SAVE_AND_NEW = 'saveAndNew',
  SAVE_AND_CLOSE = 'saveAndClose',
  CUSTOM = 'custom',
}

export enum ButtonPosition {
  LEFT = 'left',
  CENTER = 'center',
  RIGHT = 'right',
  SPACE_BETWEEN = 'space-between',
  STICKY_BOTTOM = 'sticky-bottom',
  STICKY_TOP = 'sticky-top',
}

import { Observable } from 'rxjs';
import { ValidatorFn, AsyncValidatorFn } from '@angular/forms';
import { Type } from '@angular/core';
import {
  FieldType,
  ValidationType,
  ValidationTrigger,
  ValidationSeverity,
  FieldSize,
  FieldAppearance,
  FieldLabelPosition,
} from '../enums';

export interface IFieldConfig {
  key: string;
  type: FieldType;
  label?: string | IMultiLanguageText;
  placeholder?: string | IMultiLanguageText;
  hint?: string | IMultiLanguageText;
  tooltip?: string | IMultiLanguageText;
  value?: any;
  defaultValue?: any;
  disabled?: boolean;
  readonly?: boolean;
  hidden?: boolean;
  required?: boolean;
  autofocus?: boolean;
  tabIndex?: number;
  autocomplete?: string;
  validations?: IFieldValidation[];
  conditions?: IFieldCondition[];
  options?:
    | IFieldOption[]
    | Observable<IFieldOption[]>
    | (() => Observable<IFieldOption[]>);
  optionConfig?: IOptionConfig;
  grid?: IGridConfig;
  appearance?: FieldAppearance;
  size?: FieldSize;
  labelPosition?: FieldLabelPosition;
  cssClass?: string | string[];
  containerClass?: string | string[];
  labelClass?: string | string[];
  errorClass?: string | string[];
  prefix?: IFieldAddon;
  suffix?: IFieldAddon;
  mask?: string | IInputMask;
  debounceTime?: number;
  throttleTime?: number;
  updateOn?: 'change' | 'blur' | 'submit';
  asyncDataSource?: IAsyncDataSource;
  customComponent?: Type<any>;
  customProperties?: Record<string, any>;
  metadata?: Record<string, any>;
}

export interface IMultiLanguageText {
  [languageCode: string]: string;
}

export interface IFieldValidation {
  type: ValidationType;
  value?: any;
  message?: string | IMultiLanguageText;
  severity?: ValidationSeverity;
  trigger?: ValidationTrigger | ValidationTrigger[];
  validator?: ValidatorFn;
  asyncValidator?: AsyncValidatorFn;
  debounceTime?: number;
  conditionalValidation?: IConditionalValidation;
}

export interface IConditionalValidation {
  condition: IFieldCondition | IFieldConditionGroup;
  validations: IFieldValidation[];
}

export interface IFieldCondition {
  source: string | string[];
  operator: string;
  value?: any;
  action: string;
  target?: string | string[];
  actionValue?: any;
  negate?: boolean;
}

export interface IFieldConditionGroup {
  logic: 'and' | 'or' | 'not' | 'xor';
  conditions: (IFieldCondition | IFieldConditionGroup)[];
}

export interface IFieldOption {
  value: any;
  label: string | IMultiLanguageText;
  disabled?: boolean;
  hidden?: boolean;
  icon?: string;
  iconPosition?: 'start' | 'end';
  description?: string | IMultiLanguageText;
  group?: string;
  metadata?: Record<string, any>;
  children?: IFieldOption[];
}

export interface IOptionConfig {
  valueKey?: string;
  labelKey?: string;
  disabledKey?: string;
  groupKey?: string;
  childrenKey?: string;
  iconKey?: string;
  multiple?: boolean;
  searchable?: boolean;
  clearable?: boolean;
  virtualize?: boolean;
  virtualizeItemHeight?: number;
  loadingText?: string | IMultiLanguageText;
  noDataText?: string | IMultiLanguageText;
  searchPlaceholder?: string | IMultiLanguageText;
  selectAllText?: string | IMultiLanguageText;
}

export interface IGridConfig {
  xs?: number;
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
  xxl?: number;
  colspan?: number;
  rowspan?: number;
  order?: number;
  offset?: number;
  push?: number;
  pull?: number;
}

export interface IFieldAddon {
  text?: string | IMultiLanguageText;
  icon?: string;
  iconSet?: string;
  tooltip?: string | IMultiLanguageText;
  clickable?: boolean;
  onClick?: (field: IFieldConfig, value: any) => void;
  cssClass?: string;
}

export interface IInputMask {
  mask: string;
  showMaskTyped?: boolean;
  clearIfNotMatch?: boolean;
  dropSpecialCharacters?: boolean;
  hiddenInput?: boolean;
  validation?: boolean;
  separatorLimit?: string;
  allowNegativeNumbers?: boolean;
  suffix?: string;
  prefix?: string;
  thousandSeparator?: string;
  decimalMarker?: string;
  patterns?: Record<string, IPatternOptions>;
}

export interface IPatternOptions {
  pattern: RegExp;
  optional?: boolean;
}

export interface IAsyncDataSource {
  url?: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  params?: Record<string, any> | ((query: string) => Record<string, any>);
  body?: any | ((query: string) => any);
  responseMap?: (response: any) => IFieldOption[];
  debounceTime?: number;
  minSearchLength?: number;
  cache?: boolean;
  cacheExpiry?: number;
  loadOnInit?: boolean;
  searchParam?: string;
}

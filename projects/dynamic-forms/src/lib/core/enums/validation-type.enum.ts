export enum ValidationType {
  REQUIRED = 'required',
  REQUIRED_TRUE = 'requiredTrue',
  EMAIL = 'email',
  PATTERN = 'pattern',
  MIN_LENGTH = 'minLength',
  MAX_LENGTH = 'maxLength',
  MIN = 'min',
  MAX = 'max',
  MIN_DATE = 'minDate',
  MAX_DATE = 'maxDate',
  FILE_SIZE = 'fileSize',
  FILE_TYPE = 'fileType',
  URL = 'url',
  PHONE = 'phone',
  ALPHA = 'alpha',
  ALPHANUMERIC = 'alphanumeric',
  NUMERIC = 'numeric',
  INTEGER = 'integer',
  DECIMAL = 'decimal',
  CUSTOM = 'custom',
  ASYNC = 'async',
  MATCH_FIELD = 'matchField',
  NOT_MATCH_FIELD = 'notMatchField',
  GREATER_THAN_FIELD = 'greaterThanField',
  LESS_THAN_FIELD = 'lessThanField',
  DATE_AFTER_FIELD = 'dateAfterField',
  DATE_BEFORE_FIELD = 'dateBeforeField',
  SUM_EQUALS = 'sumEquals',
  UNIQUE = 'unique',
  EXISTS = 'exists',
  NOT_EXISTS = 'notExists',
}

export enum ValidationTrigger {
  BLUR = 'blur',
  CHANGE = 'change',
  SUBMIT = 'submit',
  MANUAL = 'manual',
}

export enum ValidationSeverity {
  ERROR = 'error',
  WARNING = 'warning',
  INFO = 'info',
}

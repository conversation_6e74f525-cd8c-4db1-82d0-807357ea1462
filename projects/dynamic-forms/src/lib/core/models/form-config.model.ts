import {
  IFormConfig,
  <PERSON>orm<PERSON><PERSON>tion,
  IForm<PERSON>utton,
  IForm<PERSON>heme,
  <PERSON>ormHooks,
  IFieldConfig,
  IMultiLanguageText,
  IFormError,
  IButtonCondition,
} from '../interfaces';
import {
  FormLayout,
  FormMode,
  ButtonPosition,
  ButtonType,
  FormState,
} from '../enums';
import { FieldConfig } from './field-config.model';
import { FormConfigBuilder } from './form-config-builder.model';

export class FormConfig implements IFormConfig {
  public id?: string;
  public name?: string;
  public title?: string | IMultiLanguageText;
  public description?: string | IMultiLanguageText;
  public fields: FieldConfig[] | FormSection[];
  public layout: FormLayout;
  public mode: FormMode;
  public columns: number;
  public gap: number;
  public padding: number | string;
  public buttons: FormButton[];
  public buttonPosition: ButtonPosition;
  public submitOnEnter: boolean;
  public validateOnSubmit: boolean;
  public validateOnChange: boolean;
  public validateOnBlur: boolean;
  public resetOnSubmit: boolean;
  public showValidationSummary: boolean;
  public showRequiredIndicator: boolean;
  public requiredIndicatorPosition: 'start' | 'end';
  public requiredIndicatorText: string;
  public labelSuffix: string;
  public preserveFormState: boolean;
  public autoSave: boolean;
  public autoSaveInterval: number;
  public confirmOnCancel: boolean;
  public confirmOnNavigate: boolean;
  public cssClass?: string | string[];
  public containerClass?: string | string[];
  public theme?: IFormTheme;
  public hooks?: IFormHooks;
  public metadata: Record<string, any>;

  private _state: FormState;
  private _errors: IFormError[];
  private _isDirty: boolean;
  private _isValid: boolean;
  private _values: Record<string, any>;

  constructor(config: Partial<IFormConfig>) {
    this.id = config.id;
    this.name = config.name;
    this.title = config.title;
    this.description = config.description;
    this.fields = this.initializeFields(config.fields || []);
    this.layout = config.layout ?? FormLayout.VERTICAL;
    this.mode = config.mode ?? FormMode.CREATE;
    this.columns = config.columns ?? 1;
    this.gap = config.gap ?? 16;
    this.padding = config.padding ?? 16;
    this.buttons = this.initializeButtons(config.buttons);
    this.buttonPosition = config.buttonPosition ?? ButtonPosition.RIGHT;
    this.submitOnEnter = config.submitOnEnter ?? false;
    this.validateOnSubmit = config.validateOnSubmit ?? true;
    this.validateOnChange = config.validateOnChange ?? false;
    this.validateOnBlur = config.validateOnBlur ?? true;
    this.resetOnSubmit = config.resetOnSubmit ?? false;
    this.showValidationSummary = config.showValidationSummary ?? false;
    this.showRequiredIndicator = config.showRequiredIndicator ?? true;
    this.requiredIndicatorPosition = config.requiredIndicatorPosition ?? 'end';
    this.requiredIndicatorText = config.requiredIndicatorText ?? '*';
    this.labelSuffix = config.labelSuffix ?? ':';
    this.preserveFormState = config.preserveFormState ?? false;
    this.autoSave = config.autoSave ?? false;
    this.autoSaveInterval = config.autoSaveInterval ?? 30000;
    this.confirmOnCancel = config.confirmOnCancel ?? false;
    this.confirmOnNavigate = config.confirmOnNavigate ?? false;
    this.cssClass = config.cssClass;
    this.containerClass = config.containerClass;
    this.theme = config.theme;
    this.hooks = config.hooks;
    this.metadata = config.metadata ?? {};

    this._state = FormState.IDLE;
    this._errors = [];
    this._isDirty = false;
    this._isValid = true;
    this._values = {};
  }

  private initializeFields(
    fields: IFieldConfig[] | IFormSection[]
  ): FieldConfig[] | FormSection[] {
    if (fields.length === 0) return [];

    if (this.isFieldConfigArray(fields)) {
      return fields.map((field) =>
        field instanceof FieldConfig ? field : new FieldConfig(field)
      );
    } else {
      return fields.map((section) =>
        section instanceof FormSection ? section : new FormSection(section)
      );
    }
  }

  private isFieldConfigArray(fields: any[]): fields is IFieldConfig[] {
    return fields.length > 0 && 'type' in fields[0];
  }

  private initializeButtons(buttons?: IFormButton[]): FormButton[] {
    if (!buttons || buttons.length === 0) {
      return [
        new FormButton({
          type: ButtonType.SUBMIT,
          label: 'Submit',
        }),
        new FormButton({
          type: ButtonType.CANCEL,
          label: 'Cancel',
        }),
      ];
    }
    return buttons.map((button) =>
      button instanceof FormButton ? button : new FormButton(button)
    );
  }

  public get state(): FormState {
    return this._state;
  }

  public get errors(): IFormError[] {
    return this._errors;
  }

  public get isDirty(): boolean {
    return this._isDirty;
  }

  public get isValid(): boolean {
    return this._isValid;
  }

  public get values(): Record<string, any> {
    return this._values;
  }

  public get allFields(): FieldConfig[] {
    if (this.fields.length === 0) return [];

    if (this.fields[0] instanceof FieldConfig) {
      return this.fields as FieldConfig[];
    } else {
      const sections = this.fields as FormSection[];
      return sections.reduce((acc: FieldConfig[], section) => {
        return [...acc, ...section.fields];
      }, []);
    }
  }

  public setState(state: FormState): void {
    this._state = state;
  }

  public setErrors(errors: IFormError[]): void {
    this._errors = errors;
    this._isValid = errors.length === 0;
  }

  public addError(error: IFormError): void {
    this._errors.push(error);
    this._isValid = false;
  }

  public clearErrors(): void {
    this._errors = [];
    this._isValid = true;
  }

  public markAsDirty(): void {
    this._isDirty = true;
  }

  public markAsPristine(): void {
    this._isDirty = false;
  }

  public setValue(values: Record<string, any>): void {
    this._values = { ...values };
    this.allFields.forEach((field) => {
      if (values.hasOwnProperty(field.key)) {
        field.setValue(values[field.key]);
      }
    });
    this._isDirty = true;
  }

  public getValue(): Record<string, any> {
    const values: Record<string, any> = {};
    this.allFields.forEach((field) => {
      values[field.key] = field.value;
    });
    return values;
  }

  public getField(key: string): FieldConfig | undefined {
    return this.allFields.find((field) => field.key === key);
  }

  public getSection(id: string): FormSection | undefined {
    if (this.fields[0] instanceof FormSection) {
      const sections = this.fields as FormSection[];
      return sections.find((section) => section.id === id);
    }
    return undefined;
  }

  public reset(): void {
    this.allFields.forEach((field) => field.reset());
    this._isDirty = false;
    this._errors = [];
    this._isValid = true;
    this._state = FormState.IDLE;
  }

  public validate(): boolean {
    this._errors = [];
    let isValid = true;

    this.allFields.forEach((field) => {
      if (!field.isValid) {
        isValid = false;
        this._errors.push({
          field: field.key,
          message: 'Field validation failed',
          type: 'validation',
          severity: 'error',
        });
      }
    });

    this._isValid = isValid;
    return isValid;
  }

  public toJSON(): IFormConfig {
    return {
      id: this.id,
      name: this.name,
      title: this.title,
      description: this.description,
      fields: this.fields.map((f: any) =>
        f instanceof FieldConfig || f instanceof FormSection ? f.toJSON() : f
      ),
      layout: this.layout,
      mode: this.mode,
      columns: this.columns,
      gap: this.gap,
      padding: this.padding,
      buttons: this.buttons.map((b) => b.toJSON()),
      buttonPosition: this.buttonPosition,
      submitOnEnter: this.submitOnEnter,
      validateOnSubmit: this.validateOnSubmit,
      validateOnChange: this.validateOnChange,
      validateOnBlur: this.validateOnBlur,
      resetOnSubmit: this.resetOnSubmit,
      showValidationSummary: this.showValidationSummary,
      showRequiredIndicator: this.showRequiredIndicator,
      requiredIndicatorPosition: this.requiredIndicatorPosition,
      requiredIndicatorText: this.requiredIndicatorText,
      labelSuffix: this.labelSuffix,
      preserveFormState: this.preserveFormState,
      autoSave: this.autoSave,
      autoSaveInterval: this.autoSaveInterval,
      confirmOnCancel: this.confirmOnCancel,
      confirmOnNavigate: this.confirmOnNavigate,
      cssClass: this.cssClass,
      containerClass: this.containerClass,
      theme: this.theme,
      hooks: this.hooks,
      metadata: this.metadata,
    };
  }

  public static fromJSON(json: IFormConfig): FormConfig {
    return new FormConfig(json);
  }

  public static builder(name?: string): FormConfigBuilder {
    return new FormConfigBuilder(name);
  }
}

export class FormSection implements IFormSection {
  public id?: string;
  public title?: string | IMultiLanguageText;
  public description?: string | IMultiLanguageText;
  public fields: FieldConfig[];
  public columns: number;
  public collapsible: boolean;
  public collapsed: boolean;
  public hidden: boolean;
  public disabled: boolean;
  public icon?: string;
  public cssClass?: string;
  public conditions: any[];
  public order: number;

  constructor(config: Partial<IFormSection>) {
    this.id = config.id;
    this.title = config.title;
    this.description = config.description;
    this.fields = (config.fields || []).map((field) =>
      field instanceof FieldConfig ? field : new FieldConfig(field)
    );
    this.columns = config.columns ?? 1;
    this.collapsible = config.collapsible ?? false;
    this.collapsed = config.collapsed ?? false;
    this.hidden = config.hidden ?? false;
    this.disabled = config.disabled ?? false;
    this.icon = config.icon;
    this.cssClass = config.cssClass;
    this.conditions = config.conditions ?? [];
    this.order = config.order ?? 0;
  }

  public toggle(): void {
    if (this.collapsible) {
      this.collapsed = !this.collapsed;
    }
  }

  public expand(): void {
    if (this.collapsible) {
      this.collapsed = false;
    }
  }

  public collapse(): void {
    if (this.collapsible) {
      this.collapsed = true;
    }
  }

  public show(): void {
    this.hidden = false;
  }

  public hide(): void {
    this.hidden = true;
  }

  public enable(): void {
    this.disabled = false;
    this.fields.forEach((field) => field.enable());
  }

  public disable(): void {
    this.disabled = true;
    this.fields.forEach((field) => field.disable());
  }

  public addField(field: FieldConfig | IFieldConfig): void {
    const fieldConfig =
      field instanceof FieldConfig ? field : new FieldConfig(field);
    this.fields.push(fieldConfig);
  }

  public removeField(key: string): void {
    const index = this.fields.findIndex((field) => field.key === key);
    if (index > -1) {
      this.fields.splice(index, 1);
    }
  }

  public getField(key: string): FieldConfig | undefined {
    return this.fields.find((field) => field.key === key);
  }

  public toJSON(): IFormSection {
    return {
      id: this.id,
      title: this.title,
      description: this.description,
      fields: this.fields.map((field) => field.toJSON()),
      columns: this.columns,
      collapsible: this.collapsible,
      collapsed: this.collapsed,
      hidden: this.hidden,
      disabled: this.disabled,
      icon: this.icon,
      cssClass: this.cssClass,
      conditions: this.conditions,
      order: this.order,
    };
  }
}

export class FormButton implements IFormButton {
  public type: ButtonType;
  public label: string | IMultiLanguageText;
  public icon?: string;
  public iconPosition: 'start' | 'end';
  public cssClass?: string;
  public disabled: boolean;
  public hidden: boolean;
  public loading: boolean;
  public loadingText?: string | IMultiLanguageText;
  public tooltip?: string | IMultiLanguageText;
  public confirmation?: any;
  public onClick?: (form: any) => void | any;
  public conditions: IButtonCondition[];

  constructor(config: Partial<IFormButton>) {
    this.type = config.type ?? ButtonType.CUSTOM;
    this.label = config.label ?? 'Button';
    this.icon = config.icon;
    this.iconPosition = config.iconPosition ?? 'start';
    this.cssClass = config.cssClass;
    this.disabled = config.disabled ?? false;
    this.hidden = config.hidden ?? false;
    this.loading = config.loading ?? false;
    this.loadingText = config.loadingText;
    this.tooltip = config.tooltip;
    this.confirmation = config.confirmation;
    this.onClick = config.onClick;
    this.conditions = config.conditions ?? [];
  }

  public setLoading(
    loading: boolean,
    loadingText?: string | IMultiLanguageText
  ): void {
    this.loading = loading;
    if (loadingText) {
      this.loadingText = loadingText;
    }
  }

  public enable(): void {
    this.disabled = false;
  }

  public disable(): void {
    this.disabled = true;
  }

  public show(): void {
    this.hidden = false;
  }

  public hide(): void {
    this.hidden = true;
  }

  public toJSON(): IFormButton {
    return {
      type: this.type,
      label: this.label,
      icon: this.icon,
      iconPosition: this.iconPosition,
      cssClass: this.cssClass,
      disabled: this.disabled,
      hidden: this.hidden,
      loading: this.loading,
      loadingText: this.loadingText,
      tooltip: this.tooltip,
      confirmation: this.confirmation,
      onClick: this.onClick,
      conditions: this.conditions,
    };
  }
}

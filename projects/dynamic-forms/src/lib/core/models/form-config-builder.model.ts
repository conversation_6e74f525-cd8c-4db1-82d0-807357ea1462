import {
  IFormConfig,
  IFormSection,
  IFormButton,
  IFormTheme,
  IFormHooks,
  IFieldConfig,
  IMultiLanguageText,
} from '../interfaces';
import { FormLayout, FormMode, ButtonPosition, ButtonType } from '../enums';
import { FormConfig, FormSection, FormButton } from './form-config.model';
import { FieldConfig } from './field-config.model';

export class FormConfigBuilder {
  private readonly config: Partial<IFormConfig>;

  constructor(name?: string) {
    this.config = {
      name,
      fields: [],
      buttons: [],
      metadata: {},
    };
  }

  public withId(id: string): this {
    this.config.id = id;
    return this;
  }

  public withName(name: string): this {
    this.config.name = name;
    return this;
  }

  public withTitle(title: string | IMultiLanguageText): this {
    this.config.title = title;
    return this;
  }

  public withDescription(description: string | IMultiLanguageText): this {
    this.config.description = description;
    return this;
  }

  public addField(field: IFieldConfig | FieldConfig): this {
    const fields = this.config.fields as IFieldConfig[];
    fields.push(field instanceof FieldConfig ? field.toJSON() : field);
    return this;
  }

  public addFields(fields: (IFieldConfig | FieldConfig)[]): this {
    const configFields = this.config.fields as IFieldConfig[];
    fields.forEach((field) => {
      configFields.push(field instanceof FieldConfig ? field.toJSON() : field);
    });
    return this;
  }

  public addSection(section: IFormSection | FormSection): this {
    const sections = this.config.fields as IFormSection[];
    sections.push(section instanceof FormSection ? section.toJSON() : section);
    return this;
  }

  public addSections(sections: (IFormSection | FormSection)[]): this {
    const configSections = this.config.fields as IFormSection[];
    sections.forEach((section) => {
      configSections.push(
        section instanceof FormSection ? section.toJSON() : section
      );
    });
    return this;
  }

  public withLayout(layout: FormLayout): this {
    this.config.layout = layout;
    return this;
  }

  public withMode(mode: FormMode): this {
    this.config.mode = mode;
    return this;
  }

  public withColumns(columns: number): this {
    this.config.columns = columns;
    return this;
  }

  public withGap(gap: number): this {
    this.config.gap = gap;
    return this;
  }

  public withPadding(padding: number | string): this {
    this.config.padding = padding;
    return this;
  }

  public addButton(button: IFormButton | FormButton): this {
    this.config.buttons = this.config.buttons || [];
    this.config.buttons.push(
      button instanceof FormButton ? button.toJSON() : button
    );
    return this;
  }

  public addButtons(buttons: (IFormButton | FormButton)[]): this {
    this.config.buttons = this.config.buttons || [];
    buttons.forEach((button) => {
      this.config.buttons!.push(
        button instanceof FormButton ? button.toJSON() : button
      );
    });
    return this;
  }

  public withButtonPosition(position: ButtonPosition): this {
    this.config.buttonPosition = position;
    return this;
  }

  public withSubmitOnEnter(submitOnEnter: boolean = true): this {
    this.config.submitOnEnter = submitOnEnter;
    return this;
  }

  public withValidateOnSubmit(validateOnSubmit: boolean = true): this {
    this.config.validateOnSubmit = validateOnSubmit;
    return this;
  }

  public withValidateOnChange(validateOnChange: boolean = true): this {
    this.config.validateOnChange = validateOnChange;
    return this;
  }

  public withValidateOnBlur(validateOnBlur: boolean = true): this {
    this.config.validateOnBlur = validateOnBlur;
    return this;
  }

  public withResetOnSubmit(resetOnSubmit: boolean = true): this {
    this.config.resetOnSubmit = resetOnSubmit;
    return this;
  }

  public withValidationSummary(show: boolean = true): this {
    this.config.showValidationSummary = show;
    return this;
  }

  public withRequiredIndicator(
    show: boolean = true,
    position: 'start' | 'end' = 'end',
    text: string = '*'
  ): this {
    this.config.showRequiredIndicator = show;
    this.config.requiredIndicatorPosition = position;
    this.config.requiredIndicatorText = text;
    return this;
  }

  public withLabelSuffix(suffix: string): this {
    this.config.labelSuffix = suffix;
    return this;
  }

  public withPreserveFormState(preserve: boolean = true): this {
    this.config.preserveFormState = preserve;
    return this;
  }

  public withAutoSave(enabled: boolean = true, interval: number = 30000): this {
    this.config.autoSave = enabled;
    this.config.autoSaveInterval = interval;
    return this;
  }

  public withConfirmOnCancel(confirm: boolean = true): this {
    this.config.confirmOnCancel = confirm;
    return this;
  }

  public withConfirmOnNavigate(confirm: boolean = true): this {
    this.config.confirmOnNavigate = confirm;
    return this;
  }

  public withCssClass(cssClass: string | string[]): this {
    this.config.cssClass = cssClass;
    return this;
  }

  public withContainerClass(containerClass: string | string[]): this {
    this.config.containerClass = containerClass;
    return this;
  }

  public withTheme(theme: IFormTheme): this {
    this.config.theme = theme;
    return this;
  }

  public withHooks(hooks: IFormHooks): this {
    this.config.hooks = hooks;
    return this;
  }

  public withMetadata(key: string, value: any): this {
    this.config.metadata = this.config.metadata || {};
    this.config.metadata[key] = value;
    return this;
  }

  public withMetadataObject(metadata: Record<string, any>): this {
    this.config.metadata = { ...(this.config.metadata || {}), ...metadata };
    return this;
  }

  public build(): FormConfig {
    return new FormConfig(this.config);
  }

  public buildConfig(): IFormConfig {
    return this.build().toJSON();
  }
}

export class FormSectionBuilder {
  private readonly config: Partial<IFormSection>;

  constructor(id?: string) {
    this.config = {
      id,
      fields: [],
      conditions: [],
    };
  }

  public withId(id: string): this {
    this.config.id = id;
    return this;
  }

  public withTitle(title: string | IMultiLanguageText): this {
    this.config.title = title;
    return this;
  }

  public withDescription(description: string | IMultiLanguageText): this {
    this.config.description = description;
    return this;
  }

  public addField(field: IFieldConfig | FieldConfig): this {
    this.config.fields = this.config.fields || [];
    this.config.fields.push(
      field instanceof FieldConfig ? field.toJSON() : field
    );
    return this;
  }

  public addFields(fields: (IFieldConfig | FieldConfig)[]): this {
    this.config.fields = this.config.fields || [];
    fields.forEach((field) => {
      this.config.fields!.push(
        field instanceof FieldConfig ? field.toJSON() : field
      );
    });
    return this;
  }

  public withColumns(columns: number): this {
    this.config.columns = columns;
    return this;
  }

  public collapsible(
    collapsible: boolean = true,
    collapsed: boolean = false
  ): this {
    this.config.collapsible = collapsible;
    this.config.collapsed = collapsed;
    return this;
  }

  public hidden(hidden: boolean = true): this {
    this.config.hidden = hidden;
    return this;
  }

  public disabled(disabled: boolean = true): this {
    this.config.disabled = disabled;
    return this;
  }

  public withIcon(icon: string): this {
    this.config.icon = icon;
    return this;
  }

  public withCssClass(cssClass: string): this {
    this.config.cssClass = cssClass;
    return this;
  }

  public withOrder(order: number): this {
    this.config.order = order;
    return this;
  }

  public build(): FormSection {
    return new FormSection(this.config);
  }

  public buildConfig(): IFormSection {
    return this.build().toJSON();
  }
}

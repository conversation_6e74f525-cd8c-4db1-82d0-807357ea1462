import { Type } from '@angular/core';
import { ValidatorFn, AsyncValidatorFn } from '@angular/forms';
import { Observable, isObservable, of } from 'rxjs';
import {
  IFieldConfig,
  IFieldValidation,
  IFieldCondition,
  IFieldOption,
  IGridConfig,
  IFieldAddon,
  IInputMask,
  IAsyncDataSource,
  IOptionConfig,
  IMultiLanguageText,
} from '../interfaces';
import {
  FieldType,
  FieldSize,
  FieldAppearance,
  FieldLabelPosition,
} from '../enums';
import { FieldConfigBuilder } from './field-config-builder.model';

export class FieldConfig implements IFieldConfig {
  public key: string;
  public type: FieldType;
  public label?: string | IMultiLanguageText;
  public placeholder?: string | IMultiLanguageText;
  public hint?: string | IMultiLanguageText;
  public tooltip?: string | IMultiLanguageText;
  public value?: any;
  public defaultValue?: any;
  public disabled: boolean;
  public readonly: boolean;
  public hidden: boolean;
  public required: boolean;
  public autofocus: boolean;
  public tabIndex?: number;
  public autocomplete?: string;
  public validations: IFieldValidation[];
  public conditions: IFieldCondition[];
  public options?:
    | IFieldOption[]
    | Observable<IFieldOption[]>
    | (() => Observable<IFieldOption[]>);
  public optionConfig?: IOptionConfig;
  public grid?: IGridConfig;
  public appearance: FieldAppearance;
  public size: FieldSize;
  public labelPosition: FieldLabelPosition;
  public cssClass?: string | string[];
  public containerClass?: string | string[];
  public labelClass?: string | string[];
  public errorClass?: string | string[];
  public prefix?: IFieldAddon;
  public suffix?: IFieldAddon;
  public mask?: string | IInputMask;
  public debounceTime: number;
  public throttleTime: number;
  public updateOn: 'change' | 'blur' | 'submit';
  public asyncDataSource?: IAsyncDataSource;
  public customComponent?: Type<any>;
  public customProperties: Record<string, any>;
  public metadata: Record<string, any>;

  private readonly _validators: ValidatorFn[];
  private readonly _asyncValidators: AsyncValidatorFn[];
  private _originalValue: any;
  private _isDirty: boolean;
  private _isTouched: boolean;
  private _errors: Record<string, any>;

  constructor(config: Partial<IFieldConfig>) {
    this.key = config.key || '';
    this.type = config.type || FieldType.TEXT;
    this.label = config.label;
    this.placeholder = config.placeholder;
    this.hint = config.hint;
    this.tooltip = config.tooltip;
    this.value = config.value;
    this.defaultValue = config.defaultValue ?? config.value;
    this.disabled = config.disabled ?? false;
    this.readonly = config.readonly ?? false;
    this.hidden = config.hidden ?? false;
    this.required = config.required ?? false;
    this.autofocus = config.autofocus ?? false;
    this.tabIndex = config.tabIndex;
    this.autocomplete = config.autocomplete;
    this.validations = config.validations ?? [];
    this.conditions = config.conditions ?? [];
    this.options = config.options;
    this.optionConfig = config.optionConfig;
    this.grid = config.grid;
    this.appearance = config.appearance ?? FieldAppearance.OUTLINE;
    this.size = config.size ?? FieldSize.MEDIUM;
    this.labelPosition = config.labelPosition ?? FieldLabelPosition.ABOVE;
    this.cssClass = config.cssClass;
    this.containerClass = config.containerClass;
    this.labelClass = config.labelClass;
    this.errorClass = config.errorClass;
    this.prefix = config.prefix;
    this.suffix = config.suffix;
    this.mask = config.mask;
    this.debounceTime = config.debounceTime ?? 300;
    this.throttleTime = config.throttleTime ?? 0;
    this.updateOn = config.updateOn ?? 'change';
    this.asyncDataSource = config.asyncDataSource;
    this.customComponent = config.customComponent;
    this.customProperties = config.customProperties ?? {};
    this.metadata = config.metadata ?? {};

    this._validators = [];
    this._asyncValidators = [];
    this._originalValue = this.value;
    this._isDirty = false;
    this._isTouched = false;
    this._errors = {};

    this.initializeValidators();
    this.normalizeOptions();
  }

  private initializeValidators(): void {
    if (this.required) {
      const requiredValidation: IFieldValidation = {
        type: 'required' as any,
        message: this.getRequiredMessage(),
      };
      if (!this.validations.find((v) => v.type === 'required')) {
        this.validations.unshift(requiredValidation);
      }
    }

    this.validations.forEach((validation) => {
      if (validation.validator) {
        this._validators.push(validation.validator);
      }
      if (validation.asyncValidator) {
        this._asyncValidators.push(validation.asyncValidator);
      }
    });
  }

  private normalizeOptions(): void {
    if (this.options && typeof this.options === 'function') {
      const result = this.options();
      if (!isObservable(result)) {
        this.options = of(result as IFieldOption[]);
      } else {
        this.options = result;
      }
    }
  }

  private getRequiredMessage(): string | IMultiLanguageText {
    const { label } = this;

    if (typeof label === 'string') return `${label} is required`;

    if (!label) return 'This field is required';

    const multiLangMessage: IMultiLanguageText = {};
    Object.keys(label).forEach((lang) => {
      multiLangMessage[lang] = `${label[lang]} is required`;
    });
    return multiLangMessage;
  }

  public get validators(): ValidatorFn[] {
    return this._validators;
  }

  public get asyncValidators(): AsyncValidatorFn[] {
    return this._asyncValidators;
  }

  public get isDirty(): boolean {
    return this._isDirty;
  }

  public get isTouched(): boolean {
    return this._isTouched;
  }

  public get errors(): Record<string, any> {
    return this._errors;
  }

  public get isValid(): boolean {
    return Object.keys(this._errors).length === 0;
  }

  public get hasValue(): boolean {
    return this.value !== null && this.value !== undefined && this.value !== '';
  }

  public get isEditable(): boolean {
    return !this.disabled && !this.readonly && !this.hidden;
  }

  public setValue(
    value: any,
    options?: { emitEvent?: boolean; onlySelf?: boolean }
  ): void {
    const previousValue = this.value;
    this.value = value;

    if (value !== this._originalValue) {
      this._isDirty = true;
    }
  }

  public reset(): void {
    this.value = this.defaultValue ?? null;
    this._isDirty = false;
    this._isTouched = false;
    this._errors = {};
    this._originalValue = this.value;
  }

  public markAsTouched(): void {
    this._isTouched = true;
  }

  public markAsUntouched(): void {
    this._isTouched = false;
  }

  public markAsDirty(): void {
    this._isDirty = true;
  }

  public markAsPristine(): void {
    this._isDirty = false;
    this._originalValue = this.value;
  }

  public setErrors(errors: Record<string, any> | null): void {
    this._errors = errors || {};
  }

  public addError(key: string, error: any): void {
    this._errors[key] = error;
  }

  public removeError(key: string): void {
    delete this._errors[key];
  }

  public clearErrors(): void {
    this._errors = {};
  }

  public enable(): void {
    this.disabled = false;
  }

  public disable(): void {
    this.disabled = true;
  }

  public show(): void {
    this.hidden = false;
  }

  public hide(): void {
    this.hidden = true;
  }

  public setRequired(required: boolean): void {
    this.required = required;
    this.initializeValidators();
  }

  public addValidation(validation: IFieldValidation): void {
    this.validations.push(validation);
    if (validation.validator) {
      this._validators.push(validation.validator);
    }
    if (validation.asyncValidator) {
      this._asyncValidators.push(validation.asyncValidator);
    }
  }

  public removeValidation(type: string): void {
    const index = this.validations.findIndex((v) => v.type === type);
    if (index > -1) {
      this.validations.splice(index, 1);
      this.initializeValidators();
    }
  }

  public addCondition(condition: IFieldCondition): void {
    this.conditions.push(condition);
  }

  public removeCondition(index: number): void {
    this.conditions.splice(index, 1);
  }

  public clone(): FieldConfig {
    return new FieldConfig({
      ...this,
      validations: [...this.validations],
      conditions: [...this.conditions],
      customProperties: { ...this.customProperties },
      metadata: { ...this.metadata },
    });
  }

  public toJSON(): IFieldConfig {
    return {
      key: this.key,
      type: this.type,
      label: this.label,
      placeholder: this.placeholder,
      hint: this.hint,
      tooltip: this.tooltip,
      value: this.value,
      defaultValue: this.defaultValue,
      disabled: this.disabled,
      readonly: this.readonly,
      hidden: this.hidden,
      required: this.required,
      autofocus: this.autofocus,
      tabIndex: this.tabIndex,
      autocomplete: this.autocomplete,
      validations: this.validations,
      conditions: this.conditions,
      options: this.options,
      optionConfig: this.optionConfig,
      grid: this.grid,
      appearance: this.appearance,
      size: this.size,
      labelPosition: this.labelPosition,
      cssClass: this.cssClass,
      containerClass: this.containerClass,
      labelClass: this.labelClass,
      errorClass: this.errorClass,
      prefix: this.prefix,
      suffix: this.suffix,
      mask: this.mask,
      debounceTime: this.debounceTime,
      throttleTime: this.throttleTime,
      updateOn: this.updateOn,
      asyncDataSource: this.asyncDataSource,
      customComponent: this.customComponent,
      customProperties: this.customProperties,
      metadata: this.metadata,
    };
  }

  public static fromJSON(json: IFieldConfig): FieldConfig {
    return new FieldConfig(json);
  }

  public static builder(
    key: string,
    type: FieldType = FieldType.TEXT
  ): FieldConfigBuilder {
    return new FieldConfigBuilder(key, type);
  }
}

import { IFieldConfig } from './field-config.interface';

export interface IFieldEvent {
  type: FieldEventType;
  field: string;
  value: any;
  previousValue?: any;
  source?: FieldEventSource;
  timestamp: number;
  metadata?: Record<string, any>;
}

export enum FieldEventType {
  VALUE_CHANGE = 'valueChange',
  FOCUS = 'focus',
  BLUR = 'blur',
  KEY_DOWN = 'keyDown',
  KEY_UP = 'keyUp',
  KEY_PRESS = 'keyPress',
  CLICK = 'click',
  DOUBLE_CLICK = 'doubleClick',
  MOUSE_ENTER = 'mouseEnter',
  MOUSE_LEAVE = 'mouseLeave',
  VALIDATION_CHANGE = 'validationChange',
  STATUS_CHANGE = 'statusChange',
  VISIBILITY_CHANGE = 'visibilityChange',
  DISABLED_CHANGE = 'disabledChange',
  OPTIONS_CHANGE = 'optionsChange',
  CUSTOM = 'custom',
}

export enum FieldEventSource {
  USER = 'user',
  PROGRAM = 'program',
  CONDITION = 'condition',
  VALIDATION = 'validation',
  INITIALIZATION = 'initialization',
  RESET = 'reset',
  API = 'api',
}

export interface IFieldChangeEvent extends IFieldEvent {
  type: FieldEventType.VALUE_CHANGE;
  isValid: boolean;
  errors?: Record<string, any>;
  touched: boolean;
  dirty: boolean;
}

export interface IFieldFocusEvent extends IFieldEvent {
  type: FieldEventType.FOCUS;
  relatedTarget?: HTMLElement;
}

export interface IFieldBlurEvent extends IFieldEvent {
  type: FieldEventType.BLUR;
  relatedTarget?: HTMLElement;
  validationTriggered?: boolean;
}

export interface IFieldKeyboardEvent extends IFieldEvent {
  type:
    | FieldEventType.KEY_DOWN
    | FieldEventType.KEY_UP
    | FieldEventType.KEY_PRESS;
  key: string;
  keyCode: number;
  altKey: boolean;
  ctrlKey: boolean;
  shiftKey: boolean;
  metaKey: boolean;
}

export interface IFieldMouseEvent extends IFieldEvent {
  type:
    | FieldEventType.CLICK
    | FieldEventType.DOUBLE_CLICK
    | FieldEventType.MOUSE_ENTER
    | FieldEventType.MOUSE_LEAVE;
  x: number;
  y: number;
  button?: number;
}

export interface IFieldValidationEvent extends IFieldEvent {
  type: FieldEventType.VALIDATION_CHANGE;
  isValid: boolean;
  errors?: Record<string, any>;
  warnings?: Record<string, any>;
  info?: Record<string, any>;
}

export interface IFieldStatusEvent extends IFieldEvent {
  type: FieldEventType.STATUS_CHANGE;
  status: 'VALID' | 'INVALID' | 'PENDING' | 'DISABLED';
  previousStatus: string;
}

export interface IFormEvent {
  type: FormEventType;
  form: string;
  data?: any;
  timestamp: number;
  metadata?: Record<string, any>;
}

export enum FormEventType {
  INIT = 'init',
  READY = 'ready',
  SUBMIT = 'submit',
  SUBMIT_SUCCESS = 'submitSuccess',
  SUBMIT_ERROR = 'submitError',
  RESET = 'reset',
  CANCEL = 'cancel',
  VALUE_CHANGE = 'valueChange',
  STATUS_CHANGE = 'statusChange',
  VALIDATION_START = 'validationStart',
  VALIDATION_END = 'validationEnd',
  AUTO_SAVE = 'autoSave',
  SECTION_EXPAND = 'sectionExpand',
  SECTION_COLLAPSE = 'sectionCollapse',
  STEP_CHANGE = 'stepChange',
  DESTROY = 'destroy',
}

export interface IFormSubmitEvent extends IFormEvent {
  type: FormEventType.SUBMIT;
  values: Record<string, any>;
  isValid: boolean;
  errors?: Record<string, any>;
}

export interface IFormValueChangeEvent extends IFormEvent {
  type: FormEventType.VALUE_CHANGE;
  values: Record<string, any>;
  changedFields: string[];
  isValid: boolean;
  isDirty: boolean;
}

export interface IFormStatusChangeEvent extends IFormEvent {
  type: FormEventType.STATUS_CHANGE;
  status: 'VALID' | 'INVALID' | 'PENDING' | 'DISABLED';
  previousStatus: string;
  errors?: Record<string, any>;
}

export interface IFieldEventHandler {
  handleEvent(event: IFieldEvent): void;
  subscribe(
    eventType: FieldEventType,
    callback: (event: IFieldEvent) => void
  ): void;
  unsubscribe(
    eventType: FieldEventType,
    callback?: (event: IFieldEvent) => void
  ): void;
}

export interface IFormEventHandler {
  handleEvent(event: IFormEvent): void;
  subscribe(
    eventType: FormEventType,
    callback: (event: IFormEvent) => void
  ): void;
  unsubscribe(
    eventType: FormEventType,
    callback?: (event: IFormEvent) => void
  ): void;
}

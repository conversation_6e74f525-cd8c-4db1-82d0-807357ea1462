import { Observable } from 'rxjs';
import { IFieldConfig, IMultiLanguageText } from './field-config.interface';
import { FormLayout, FormMode, ButtonType, ButtonPosition } from '../enums';

export interface IFormConfig {
  id?: string;
  name?: string;
  title?: string | IMultiLanguageText;
  description?: string | IMultiLanguageText;
  fields: IFieldConfig[] | IFormSection[];
  layout?: FormLayout;
  mode?: FormMode;
  columns?: number;
  gap?: number;
  padding?: number | string;
  buttons?: IFormButton[];
  buttonPosition?: ButtonPosition;
  submitOnEnter?: boolean;
  validateOnSubmit?: boolean;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  resetOnSubmit?: boolean;
  showValidationSummary?: boolean;
  showRequiredIndicator?: boolean;
  requiredIndicatorPosition?: 'start' | 'end';
  requiredIndicatorText?: string;
  labelSuffix?: string;
  preserveFormState?: boolean;
  autoSave?: boolean;
  autoSaveInterval?: number;
  confirmOnCancel?: boolean;
  confirmOnNavigate?: boolean;
  cssClass?: string | string[];
  containerClass?: string | string[];
  theme?: IFormTheme;
  hooks?: IFormHooks;
  metadata?: Record<string, any>;
}

export interface IFormSection {
  id?: string;
  title?: string | IMultiLanguageText;
  description?: string | IMultiLanguageText;
  fields: IFieldConfig[];
  columns?: number;
  collapsible?: boolean;
  collapsed?: boolean;
  hidden?: boolean;
  disabled?: boolean;
  icon?: string;
  cssClass?: string;
  conditions?: IFormSectionCondition[];
  order?: number;
}

export interface IFormSectionCondition {
  fields: string[];
  operator: string;
  value?: any;
  action: 'show' | 'hide' | 'enable' | 'disable' | 'expand' | 'collapse';
}

export interface IFormButton {
  type: ButtonType;
  label: string | IMultiLanguageText;
  icon?: string;
  iconPosition?: 'start' | 'end';
  cssClass?: string;
  disabled?: boolean;
  hidden?: boolean;
  loading?: boolean;
  loadingText?: string | IMultiLanguageText;
  tooltip?: string | IMultiLanguageText;
  confirmation?: IButtonConfirmation;
  onClick?: (form: any) => void | Observable<any> | Promise<any>;
  conditions?: IButtonCondition[];
}

export interface IButtonConfirmation {
  title: string | IMultiLanguageText;
  message: string | IMultiLanguageText;
  confirmText?: string | IMultiLanguageText;
  cancelText?: string | IMultiLanguageText;
  type?: 'info' | 'warning' | 'error' | 'success';
}

export interface IButtonCondition {
  formState?:
    | 'valid'
    | 'invalid'
    | 'dirty'
    | 'pristine'
    | 'touched'
    | 'untouched';
  fields?: Record<string, any>;
  custom?: (form: any) => boolean;
  action: 'show' | 'hide' | 'enable' | 'disable';
}

export interface IFormTheme {
  primaryColor?: string;
  secondaryColor?: string;
  errorColor?: string;
  warningColor?: string;
  successColor?: string;
  infoColor?: string;
  borderRadius?: string;
  fontSize?: string;
  fontFamily?: string;
  inputHeight?: string;
  labelColor?: string;
  borderColor?: string;
  focusColor?: string;
  disabledColor?: string;
  backgroundColor?: string;
  customCss?: string;
}

export interface IFormHooks {
  beforeInit?: (config: IFormConfig) => void | IFormConfig;
  afterInit?: (form: any) => void;
  beforeSubmit?: (value: any) => any | Observable<any> | Promise<any>;
  afterSubmit?: (response: any) => void;
  beforeValidation?: (form: any) => void;
  afterValidation?: (errors: any) => void;
  beforeReset?: () => boolean;
  afterReset?: () => void;
  beforeCancel?: () => boolean;
  afterCancel?: () => void;
  onFieldChange?: (field: string, value: any, form: any) => void;
  onFieldBlur?: (field: string, value: any) => void;
  onFieldFocus?: (field: string) => void;
  onFormStateChange?: (state: string) => void;
  onError?: (error: any) => void;
}

export interface IFormSubmitResult {
  success: boolean;
  data?: any;
  errors?: IFormError[];
  message?: string | IMultiLanguageText;
}

export interface IFormError {
  field?: string;
  message: string | IMultiLanguageText;
  type?: string;
  severity?: 'error' | 'warning' | 'info';
}

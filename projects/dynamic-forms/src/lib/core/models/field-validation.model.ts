import { ValidatorFn, AsyncValidatorFn } from '@angular/forms';
import {
  IFieldValidation,
  IConditionalValidation,
  IFieldCondition,
  IFieldConditionGroup,
  IMultiLanguageText,
} from '../interfaces';
import {
  ValidationType,
  ValidationTrigger,
  ValidationSeverity,
} from '../enums';

export class FieldValidation implements IFieldValidation {
  public type: ValidationType;
  public value?: any;
  public message?: string | IMultiLanguageText;
  public severity: ValidationSeverity;
  public trigger: ValidationTrigger | ValidationTrigger[];
  public validator?: ValidatorFn;
  public asyncValidator?: AsyncValidatorFn;
  public debounceTime: number;
  public conditionalValidation?: IConditionalValidation;

  private _isActive: boolean;
  private _lastValidationResult: any;
  private _validationCount: number;

  constructor(config: Partial<IFieldValidation>) {
    this.type = config.type ?? ValidationType.CUSTOM;
    this.value = config.value;
    this.message = config.message;
    this.severity = config.severity ?? ValidationSeverity.ERROR;
    this.trigger = config.trigger ?? ValidationTrigger.CHANGE;
    this.validator = config.validator;
    this.asyncValidator = config.asyncValidator;
    this.debounceTime = config.debounceTime ?? 300;
    this.conditionalValidation = config.conditionalValidation;

    this._isActive = true;
    this._lastValidationResult = null;
    this._validationCount = 0;
  }

  public get isActive(): boolean {
    return this._isActive;
  }

  public get lastValidationResult(): any {
    return this._lastValidationResult;
  }

  public get validationCount(): number {
    return this._validationCount;
  }

  public get isAsync(): boolean {
    return !!this.asyncValidator;
  }

  public get hasCondition(): boolean {
    return !!this.conditionalValidation;
  }

  public activate(): void {
    this._isActive = true;
  }

  public deactivate(): void {
    this._isActive = false;
  }

  public setValidationResult(result: any): void {
    this._lastValidationResult = result;
    this._validationCount++;
  }

  public clearValidationResult(): void {
    this._lastValidationResult = null;
  }

  public shouldTriggerOn(trigger: ValidationTrigger): boolean {
    if (Array.isArray(this.trigger)) {
      return this.trigger.includes(trigger);
    }
    return this.trigger === trigger;
  }

  public getErrorMessage(fieldLabel?: string): string {
    if (typeof this.message === 'string') {
      return this.message.replace('{field}', fieldLabel || 'Field');
    }
    return this.getDefaultMessage(fieldLabel);
  }

  private getDefaultMessage(fieldLabel?: string): string {
    const field = fieldLabel || 'Field';

    switch (this.type) {
      case ValidationType.REQUIRED:
        return `${field} is required`;
      case ValidationType.EMAIL:
        return `${field} must be a valid email address`;
      case ValidationType.MIN_LENGTH:
        return `${field} must be at least ${this.value} characters`;
      case ValidationType.MAX_LENGTH:
        return `${field} must not exceed ${this.value} characters`;
      case ValidationType.MIN:
        return `${field} must be at least ${this.value}`;
      case ValidationType.MAX:
        return `${field} must not exceed ${this.value}`;
      case ValidationType.PATTERN:
        return `${field} format is invalid`;
      case ValidationType.URL:
        return `${field} must be a valid URL`;
      case ValidationType.PHONE:
        return `${field} must be a valid phone number`;
      case ValidationType.NUMERIC:
        return `${field} must be a number`;
      case ValidationType.INTEGER:
        return `${field} must be an integer`;
      case ValidationType.ALPHA:
        return `${field} must contain only letters`;
      case ValidationType.ALPHANUMERIC:
        return `${field} must contain only letters and numbers`;
      case ValidationType.MIN_DATE:
        return `${field} must be after ${this.value}`;
      case ValidationType.MAX_DATE:
        return `${field} must be before ${this.value}`;
      case ValidationType.FILE_SIZE:
        return `File size must not exceed ${this.value} bytes`;
      case ValidationType.FILE_TYPE:
        return `File type must be ${this.value}`;
      default:
        return `${field} is invalid`;
    }
  }

  public clone(): FieldValidation {
    return new FieldValidation({
      type: this.type,
      value: this.value,
      message: this.message,
      severity: this.severity,
      trigger: this.trigger,
      validator: this.validator,
      asyncValidator: this.asyncValidator,
      debounceTime: this.debounceTime,
      conditionalValidation: this.conditionalValidation,
    });
  }

  public toJSON(): IFieldValidation {
    return {
      type: this.type,
      value: this.value,
      message: this.message,
      severity: this.severity,
      trigger: this.trigger,
      validator: this.validator,
      asyncValidator: this.asyncValidator,
      debounceTime: this.debounceTime,
      conditionalValidation: this.conditionalValidation,
    };
  }

  public static fromJSON(json: IFieldValidation): FieldValidation {
    return new FieldValidation(json);
  }

  public static builder(type: ValidationType): FieldValidationBuilder {
    return new FieldValidationBuilder(type);
  }
}

export class FieldValidationBuilder {
  private readonly config: Partial<IFieldValidation>;

  constructor(type: ValidationType) {
    this.config = {
      type,
      severity: ValidationSeverity.ERROR,
      trigger: ValidationTrigger.CHANGE,
    };
  }

  public withValue(value: any): this {
    this.config.value = value;
    return this;
  }

  public withMessage(message: string | IMultiLanguageText): this {
    this.config.message = message;
    return this;
  }

  public withSeverity(severity: ValidationSeverity): this {
    this.config.severity = severity;
    return this;
  }

  public withTrigger(trigger: ValidationTrigger | ValidationTrigger[]): this {
    this.config.trigger = trigger;
    return this;
  }

  public withValidator(validator: ValidatorFn): this {
    this.config.validator = validator;
    return this;
  }

  public withAsyncValidator(asyncValidator: AsyncValidatorFn): this {
    this.config.asyncValidator = asyncValidator;
    return this;
  }

  public withDebounceTime(debounceTime: number): this {
    this.config.debounceTime = debounceTime;
    return this;
  }

  public withCondition(
    condition: IFieldCondition | IFieldConditionGroup
  ): this {
    this.config.conditionalValidation = {
      condition,
      validations: [],
    };
    return this;
  }

  public build(): FieldValidation {
    return new FieldValidation(this.config);
  }

  public buildConfig(): IFieldValidation {
    return this.build().toJSON();
  }
}

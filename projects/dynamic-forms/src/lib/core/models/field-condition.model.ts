import { IFieldCondition, IFieldConditionGroup } from '../interfaces';
import { ConditionOperator, ConditionAction, ConditionLogic } from '../enums';

export class FieldCondition implements IFieldCondition {
  public source: string | string[];
  public operator: string;
  public value?: any;
  public action: string;
  public target?: string | string[];
  public actionValue?: any;
  public negate: boolean;

  private _evaluationCount: number;
  private _lastEvaluationResult: boolean;
  private _lastEvaluationTime: Date | null;

  constructor(config: Partial<IFieldCondition>) {
    this.source = config.source || '';
    this.operator = config.operator || ConditionOperator.EQUALS;
    this.value = config.value;
    this.action = config.action || ConditionAction.SHOW;
    this.target = config.target;
    this.actionValue = config.actionValue;
    this.negate = config.negate ?? false;

    this._evaluationCount = 0;
    this._lastEvaluationResult = false;
    this._lastEvaluationTime = null;
  }

  public get evaluationCount(): number {
    return this._evaluationCount;
  }

  public get lastEvaluationResult(): boolean {
    return this._lastEvaluationResult;
  }

  public get lastEvaluationTime(): Date | null {
    return this._lastEvaluationTime;
  }

  public evaluate(formValues: Record<string, any>): boolean {
    const sourceValues = this.getSourceValues(formValues);
    let result = false;

    switch (this.operator) {
      case ConditionOperator.EQUALS:
        result = this.evaluateEquals(sourceValues);
        break;
      case ConditionOperator.NOT_EQUALS:
        result = this.evaluateNotEquals(sourceValues);
        break;
      case ConditionOperator.GREATER_THAN:
        result = this.evaluateGreaterThan(sourceValues);
        break;
      case ConditionOperator.GREATER_THAN_OR_EQUALS:
        result = this.evaluateGreaterThanOrEquals(sourceValues);
        break;
      case ConditionOperator.LESS_THAN:
        result = this.evaluateLessThan(sourceValues);
        break;
      case ConditionOperator.LESS_THAN_OR_EQUALS:
        result = this.evaluateLessThanOrEquals(sourceValues);
        break;
      case ConditionOperator.CONTAINS:
        result = this.evaluateContains(sourceValues);
        break;
      case ConditionOperator.NOT_CONTAINS:
        result = this.evaluateNotContains(sourceValues);
        break;
      case ConditionOperator.STARTS_WITH:
        result = this.evaluateStartsWith(sourceValues);
        break;
      case ConditionOperator.ENDS_WITH:
        result = this.evaluateEndsWith(sourceValues);
        break;
      case ConditionOperator.IN:
        result = this.evaluateIn(sourceValues);
        break;
      case ConditionOperator.NOT_IN:
        result = this.evaluateNotIn(sourceValues);
        break;
      case ConditionOperator.BETWEEN:
        result = this.evaluateBetween(sourceValues);
        break;
      case ConditionOperator.EMPTY:
        result = this.evaluateEmpty(sourceValues);
        break;
      case ConditionOperator.NOT_EMPTY:
        result = this.evaluateNotEmpty(sourceValues);
        break;
      case ConditionOperator.NULL:
        result = this.evaluateNull(sourceValues);
        break;
      case ConditionOperator.NOT_NULL:
        result = this.evaluateNotNull(sourceValues);
        break;
      case ConditionOperator.TRUE:
        result = this.evaluateTrue(sourceValues);
        break;
      case ConditionOperator.FALSE:
        result = this.evaluateFalse(sourceValues);
        break;
      case ConditionOperator.REGEX:
        result = this.evaluateRegex(sourceValues);
        break;
    }

    if (this.negate) {
      result = !result;
    }

    this._lastEvaluationResult = result;
    this._lastEvaluationTime = new Date();
    this._evaluationCount++;

    return result;
  }

  private getSourceValues(formValues: Record<string, any>): any[] {
    if (Array.isArray(this.source)) {
      return this.source.map((key) => formValues[key]);
    }
    return [formValues[this.source]];
  }

  private evaluateEquals(sourceValues: any[]): boolean {
    return sourceValues.every((value) => value === this.value);
  }

  private evaluateNotEquals(sourceValues: any[]): boolean {
    return sourceValues.every((value) => value !== this.value);
  }

  private evaluateGreaterThan(sourceValues: any[]): boolean {
    return sourceValues.every((value) => value > this.value);
  }

  private evaluateGreaterThanOrEquals(sourceValues: any[]): boolean {
    return sourceValues.every((value) => value >= this.value);
  }

  private evaluateLessThan(sourceValues: any[]): boolean {
    return sourceValues.every((value) => value < this.value);
  }

  private evaluateLessThanOrEquals(sourceValues: any[]): boolean {
    return sourceValues.every((value) => value <= this.value);
  }

  private evaluateContains(sourceValues: any[]): boolean {
    return sourceValues.every((value) => {
      if (typeof value === 'string') {
        return value.includes(this.value);
      }
      if (Array.isArray(value)) {
        return value.includes(this.value);
      }
      return false;
    });
  }

  private evaluateNotContains(sourceValues: any[]): boolean {
    return !this.evaluateContains(sourceValues);
  }

  private evaluateStartsWith(sourceValues: any[]): boolean {
    return sourceValues.every((value) => {
      if (typeof value === 'string') {
        return value.startsWith(this.value);
      }
      return false;
    });
  }

  private evaluateEndsWith(sourceValues: any[]): boolean {
    return sourceValues.every((value) => {
      if (typeof value === 'string') {
        return value.endsWith(this.value);
      }
      return false;
    });
  }

  private evaluateIn(sourceValues: any[]): boolean {
    if (!Array.isArray(this.value)) return false;
    return sourceValues.every((value) => this.value.includes(value));
  }

  private evaluateNotIn(sourceValues: any[]): boolean {
    if (!Array.isArray(this.value)) return false;
    return sourceValues.every((value) => !this.value.includes(value));
  }

  private evaluateBetween(sourceValues: any[]): boolean {
    if (!Array.isArray(this.value) || this.value.length !== 2) return false;
    const [min, max] = this.value;
    return sourceValues.every((value) => value >= min && value <= max);
  }

  private evaluateEmpty(sourceValues: any[]): boolean {
    return sourceValues.every((value) => {
      if (value === null || value === undefined) return true;
      if (typeof value === 'string') return value.trim() === '';
      if (Array.isArray(value)) return value.length === 0;
      if (typeof value === 'object') return Object.keys(value).length === 0;
      return false;
    });
  }

  private evaluateNotEmpty(sourceValues: any[]): boolean {
    return !this.evaluateEmpty(sourceValues);
  }

  private evaluateNull(sourceValues: any[]): boolean {
    return sourceValues.every((value) => value === null || value === undefined);
  }

  private evaluateNotNull(sourceValues: any[]): boolean {
    return sourceValues.every((value) => value !== null && value !== undefined);
  }

  private evaluateTrue(sourceValues: any[]): boolean {
    return sourceValues.every((value) => value === true);
  }

  private evaluateFalse(sourceValues: any[]): boolean {
    return sourceValues.every((value) => value === false);
  }

  private evaluateRegex(sourceValues: any[]): boolean {
    if (typeof this.value !== 'string') return false;
    const regex = new RegExp(this.value);
    return sourceValues.every((value) => {
      if (typeof value === 'string') {
        return regex.test(value);
      }
      return false;
    });
  }

  public getTargets(): string[] {
    if (!this.target) return [];
    if (Array.isArray(this.target)) return this.target;
    return [this.target];
  }

  public clone(): FieldCondition {
    return new FieldCondition({
      source: this.source,
      operator: this.operator,
      value: this.value,
      action: this.action,
      target: this.target,
      actionValue: this.actionValue,
      negate: this.negate,
    });
  }

  public toJSON(): IFieldCondition {
    return {
      source: this.source,
      operator: this.operator,
      value: this.value,
      action: this.action,
      target: this.target,
      actionValue: this.actionValue,
      negate: this.negate,
    };
  }

  public static fromJSON(json: IFieldCondition): FieldCondition {
    return new FieldCondition(json);
  }

  public static builder(): FieldConditionBuilder {
    return new FieldConditionBuilder();
  }
}

export class FieldConditionGroup implements IFieldConditionGroup {
  public logic: 'and' | 'or' | 'not' | 'xor';
  public conditions: (FieldCondition | FieldConditionGroup)[];

  private _evaluationCount: number;
  private _lastEvaluationResult: boolean;
  private _lastEvaluationTime: Date | null;

  constructor(config: Partial<IFieldConditionGroup>) {
    this.logic = config.logic || 'and';
    this.conditions = (config.conditions || []).map((condition) => {
      if ('logic' in condition) {
        return condition instanceof FieldConditionGroup
          ? condition
          : new FieldConditionGroup(condition);
      } else {
        return condition instanceof FieldCondition
          ? condition
          : new FieldCondition(condition);
      }
    });

    this._evaluationCount = 0;
    this._lastEvaluationResult = false;
    this._lastEvaluationTime = null;
  }

  public get evaluationCount(): number {
    return this._evaluationCount;
  }

  public get lastEvaluationResult(): boolean {
    return this._lastEvaluationResult;
  }

  public get lastEvaluationTime(): Date | null {
    return this._lastEvaluationTime;
  }

  public evaluate(formValues: Record<string, any>): boolean {
    let result = false;
    const results = this.conditions.map((condition) =>
      condition.evaluate(formValues)
    );

    switch (this.logic) {
      case 'and':
        result = results.every((r) => r === true);
        break;
      case 'or':
        result = results.some((r) => r === true);
        break;
      case 'not':
        result = !results[0];
        break;
      case 'xor':
        const trueCount = results.filter((r) => r === true).length;
        result = trueCount === 1;
        break;
    }

    this._lastEvaluationResult = result;
    this._lastEvaluationTime = new Date();
    this._evaluationCount++;

    return result;
  }

  public addCondition(condition: FieldCondition | FieldConditionGroup): void {
    this.conditions.push(condition);
  }

  public removeCondition(index: number): void {
    this.conditions.splice(index, 1);
  }

  public getAllTargets(): string[] {
    const targets: string[] = [];

    this.conditions.forEach((condition) => {
      if (condition instanceof FieldCondition) {
        targets.push(...condition.getTargets());
      } else if (condition instanceof FieldConditionGroup) {
        targets.push(...condition.getAllTargets());
      }
    });

    return [...new Set(targets)];
  }

  public clone(): FieldConditionGroup {
    return new FieldConditionGroup({
      logic: this.logic,
      conditions: this.conditions.map((c) => c.clone()),
    });
  }

  public toJSON(): IFieldConditionGroup {
    return {
      logic: this.logic,
      conditions: this.conditions.map((c) => c.toJSON()),
    };
  }

  public static fromJSON(json: IFieldConditionGroup): FieldConditionGroup {
    return new FieldConditionGroup(json);
  }

  public static builder(
    logic: 'and' | 'or' | 'not' | 'xor' = 'and'
  ): FieldConditionGroupBuilder {
    return new FieldConditionGroupBuilder(logic);
  }
}

export class FieldConditionBuilder {
  private config: Partial<IFieldCondition>;

  constructor() {
    this.config = {
      operator: ConditionOperator.EQUALS,
      action: ConditionAction.SHOW,
      negate: false,
    };
  }

  public whenField(source: string | string[]): FieldConditionBuilder {
    this.config.source = source;
    return this;
  }

  public equals(value: any): FieldConditionBuilder {
    this.config.operator = ConditionOperator.EQUALS;
    this.config.value = value;
    return this;
  }

  public notEquals(value: any): FieldConditionBuilder {
    this.config.operator = ConditionOperator.NOT_EQUALS;
    this.config.value = value;
    return this;
  }

  public greaterThan(value: any): FieldConditionBuilder {
    this.config.operator = ConditionOperator.GREATER_THAN;
    this.config.value = value;
    return this;
  }

  public lessThan(value: any): FieldConditionBuilder {
    this.config.operator = ConditionOperator.LESS_THAN;
    this.config.value = value;
    return this;
  }

  public contains(value: any): FieldConditionBuilder {
    this.config.operator = ConditionOperator.CONTAINS;
    this.config.value = value;
    return this;
  }

  public isEmpty(): FieldConditionBuilder {
    this.config.operator = ConditionOperator.EMPTY;
    return this;
  }

  public isNotEmpty(): FieldConditionBuilder {
    this.config.operator = ConditionOperator.NOT_EMPTY;
    return this;
  }

  public isNull(): FieldConditionBuilder {
    this.config.operator = ConditionOperator.NULL;
    return this;
  }

  public isNotNull(): FieldConditionBuilder {
    this.config.operator = ConditionOperator.NOT_NULL;
    return this;
  }

  public isTrue(): FieldConditionBuilder {
    this.config.operator = ConditionOperator.TRUE;
    return this;
  }

  public isFalse(): FieldConditionBuilder {
    this.config.operator = ConditionOperator.FALSE;
    return this;
  }

  public then(action: string): FieldConditionBuilder {
    this.config.action = action;
    return this;
  }

  public show(target?: string | string[]): FieldConditionBuilder {
    this.config.action = ConditionAction.SHOW;
    if (target) this.config.target = target;
    return this;
  }

  public hide(target?: string | string[]): FieldConditionBuilder {
    this.config.action = ConditionAction.HIDE;
    if (target) this.config.target = target;
    return this;
  }

  public enable(target?: string | string[]): FieldConditionBuilder {
    this.config.action = ConditionAction.ENABLE;
    if (target) this.config.target = target;
    return this;
  }

  public disable(target?: string | string[]): FieldConditionBuilder {
    this.config.action = ConditionAction.DISABLE;
    if (target) this.config.target = target;
    return this;
  }

  public require(target?: string | string[]): FieldConditionBuilder {
    this.config.action = ConditionAction.REQUIRE;
    if (target) this.config.target = target;
    return this;
  }

  public setValue(
    value: any,
    target?: string | string[]
  ): FieldConditionBuilder {
    this.config.action = ConditionAction.SET_VALUE;
    this.config.actionValue = value;
    if (target) this.config.target = target;
    return this;
  }

  public withActionValue(value: any): FieldConditionBuilder {
    this.config.actionValue = value;
    return this;
  }

  public negate(negate: boolean = true): FieldConditionBuilder {
    this.config.negate = negate;
    return this;
  }

  public build(): FieldCondition {
    return new FieldCondition(this.config);
  }

  public buildConfig(): IFieldCondition {
    return this.build().toJSON();
  }
}

export class FieldConditionGroupBuilder {
  private config: Partial<IFieldConditionGroup>;

  constructor(logic: 'and' | 'or' | 'not' | 'xor' = 'and') {
    this.config = {
      logic,
      conditions: [],
    };
  }

  public withLogic(
    logic: 'and' | 'or' | 'not' | 'xor'
  ): FieldConditionGroupBuilder {
    this.config.logic = logic;
    return this;
  }

  public addCondition(
    condition: FieldCondition | IFieldCondition
  ): FieldConditionGroupBuilder {
    const fieldCondition =
      condition instanceof FieldCondition
        ? condition
        : new FieldCondition(condition);
    this.config.conditions!.push(fieldCondition);
    return this;
  }

  public addGroup(
    group: FieldConditionGroup | IFieldConditionGroup
  ): FieldConditionGroupBuilder {
    const conditionGroup =
      group instanceof FieldConditionGroup
        ? group
        : new FieldConditionGroup(group);
    this.config.conditions!.push(conditionGroup);
    return this;
  }

  public build(): FieldConditionGroup {
    return new FieldConditionGroup(this.config);
  }

  public buildConfig(): IFieldConditionGroup {
    return this.build().toJSON();
  }
}

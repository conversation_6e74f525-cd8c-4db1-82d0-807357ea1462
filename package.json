{"name": "ng-omar", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "clean": "<PERSON><PERSON><PERSON> dist", "watch-lib:core": "ng build core --watch", "watch-lib:ui": "wait-on dist/core/public-api.d.ts && ng build ui --watch", "watch-lib:query-builder": "wait-on dist/core/public-api.d.ts && ng build query-builder --watch", "watch-lib:crud-core": "wait-on dist/query-builder/public-api.d.ts && ng build crud-core --watch", "watch-lib:dynamic-entity": "wait-on dist/crud-core/public-api.d.ts && ng build dynamic-entity --watch", "serve:sample": "wait-on dist/dynamic-entity/public-api.d.ts && ng serve sample", "watch-all": "npm run clean && run-p watch-lib:* serve:sample"}, "private": true, "dependencies": {"@angular/animations": "~12.1.0", "@angular/common": "~12.1.0", "@angular/compiler": "~12.1.0", "@angular/core": "~12.1.0", "@angular/forms": "~12.1.0", "@angular/platform-browser": "~12.1.0", "@angular/platform-browser-dynamic": "~12.1.0", "@angular/router": "~12.1.0", "rxjs": "~6.6.0", "tslib": "^2.2.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~12.1.4", "@angular/cli": "~12.1.4", "@angular/compiler-cli": "~12.1.0", "@types/jasmine": "~3.8.0", "@types/minimatch": "^3.0.5", "@types/node": "^12.11.1", "jasmine-core": "~3.8.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "ng-packagr": "^12.1.0", "npm-run-all": "^4.1.5", "rimraf": "^6.0.1", "tailwindcss": "^3.4.17", "typescript": "~4.3.2", "wait-on": "^8.0.4"}}
export enum ConditionOperator {
  EQUALS = 'equals',
  NOT_EQUALS = 'notEquals',
  GREATER_THAN = 'greaterThan',
  GREATER_THAN_OR_EQUALS = 'greaterThanOrEquals',
  LESS_THAN = 'lessThan',
  LESS_THAN_OR_EQUALS = 'lessThanOrEquals',
  CONTAINS = 'contains',
  NOT_CONTAINS = 'notContains',
  STARTS_WITH = 'startsWith',
  ENDS_WITH = 'endsWith',
  IN = 'in',
  NOT_IN = 'notIn',
  BETWEEN = 'between',
  NOT_BETWEEN = 'notBetween',
  EMPTY = 'empty',
  NOT_EMPTY = 'notEmpty',
  NULL = 'null',
  NOT_NULL = 'notNull',
  TRUE = 'true',
  FALSE = 'false',
  REGEX = 'regex',
  LENGTH_EQUALS = 'lengthEquals',
  LENGTH_GREATER_THAN = 'lengthGreaterThan',
  LENGTH_LESS_THAN = 'lengthLessThan',
  DATE_EQUALS = 'dateEquals',
  DATE_BEFORE = 'dateBefore',
  DATE_AFTER = 'dateAfter',
  DATE_BETWEEN = 'dateBetween',
  ARRAY_CONTAINS = 'arrayContains',
  ARRAY_NOT_CONTAINS = 'arrayNotContains',
  ARRAY_LENGTH_EQUALS = 'arrayLengthEquals',
}

export enum ConditionAction {
  SHOW = 'show',
  HIDE = 'hide',
  ENABLE = 'enable',
  DISABLE = 'disable',
  REQUIRE = 'require',
  UNREQUIRE = 'unrequire',
  SET_VALUE = 'setValue',
  CLEAR_VALUE = 'clearValue',
  SET_OPTIONS = 'setOptions',
  CLEAR_OPTIONS = 'clearOptions',
  ADD_VALIDATION = 'addValidation',
  REMOVE_VALIDATION = 'removeValidation',
  ADD_CLASS = 'addClass',
  REMOVE_CLASS = 'removeClass',
  TRIGGER_EVENT = 'triggerEvent',
}

export enum ConditionLogic {
  AND = 'and',
  OR = 'or',
  NOT = 'not',
  XOR = 'xor',
}

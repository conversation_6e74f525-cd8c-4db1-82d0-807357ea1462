import { Type } from '@angular/core';
import { ValidatorFn, AsyncValidatorFn } from '@angular/forms';
import { Observable } from 'rxjs';
import {
  IFieldConfig,
  IFieldValidation,
  IFieldCondition,
  IFieldOption,
  IGridConfig,
  IFieldAddon,
  IInputMask,
  IAsyncDataSource,
  IOptionConfig,
  IMultiLanguageText,
} from '../interfaces';
import {
  FieldType,
  FieldSize,
  FieldAppearance,
  FieldLabelPosition,
} from '../enums';
import { FieldConfig } from './field-config.model';

export class FieldConfigBuilder {
  private readonly config: Partial<IFieldConfig>;

  constructor(key: string, type: FieldType = FieldType.TEXT) {
    this.config = {
      key,
      type,
      validations: [],
      conditions: [],
      customProperties: {},
      metadata: {},
    };
  }

  public withLabel(label: string | IMultiLanguageText): FieldConfigBuilder {
    this.config.label = label;
    return this;
  }

  public withPlaceholder(
    placeholder: string | IMultiLanguageText
  ): FieldConfigBuilder {
    this.config.placeholder = placeholder;
    return this;
  }

  public withHint(hint: string | IMultiLanguageText): FieldConfigBuilder {
    this.config.hint = hint;
    return this;
  }

  public withTooltip(tooltip: string | IMultiLanguageText): FieldConfigBuilder {
    this.config.tooltip = tooltip;
    return this;
  }

  public withValue(value: any): FieldConfigBuilder {
    this.config.value = value;
    return this;
  }

  public withDefaultValue(defaultValue: any): FieldConfigBuilder {
    this.config.defaultValue = defaultValue;
    return this;
  }

  public required(message?: string | IMultiLanguageText): FieldConfigBuilder {
    this.config.required = true;
    if (message) {
      this.addValidation({
        type: 'required' as any,
        message,
      });
    }
    return this;
  }

  public disabled(disabled: boolean = true): FieldConfigBuilder {
    this.config.disabled = disabled;
    return this;
  }

  public readonly(readonly: boolean = true): FieldConfigBuilder {
    this.config.readonly = readonly;
    return this;
  }

  public hidden(hidden: boolean = true): FieldConfigBuilder {
    this.config.hidden = hidden;
    return this;
  }

  public withAutofocus(autofocus: boolean = true): FieldConfigBuilder {
    this.config.autofocus = autofocus;
    return this;
  }

  public withTabIndex(tabIndex: number): FieldConfigBuilder {
    this.config.tabIndex = tabIndex;
    return this;
  }

  public withAutocomplete(autocomplete: string): FieldConfigBuilder {
    this.config.autocomplete = autocomplete;
    return this;
  }

  public withValidation(validation: IFieldValidation): FieldConfigBuilder {
    this.addValidation(validation);
    return this;
  }

  public withValidations(validations: IFieldValidation[]): FieldConfigBuilder {
    this.config.validations = [
      ...(this.config.validations || []),
      ...validations,
    ];
    return this;
  }

  public withValidator(
    validator: ValidatorFn,
    message?: string | IMultiLanguageText
  ): FieldConfigBuilder {
    this.addValidation({
      type: 'custom' as any,
      validator,
      message,
    });
    return this;
  }

  public withAsyncValidator(
    asyncValidator: AsyncValidatorFn,
    message?: string | IMultiLanguageText
  ): FieldConfigBuilder {
    this.addValidation({
      type: 'async' as any,
      asyncValidator,
      message,
    });
    return this;
  }

  public withCondition(condition: IFieldCondition): FieldConfigBuilder {
    this.config.conditions = this.config.conditions || [];
    this.config.conditions.push(condition);
    return this;
  }

  public withConditions(conditions: IFieldCondition[]): FieldConfigBuilder {
    this.config.conditions = [...(this.config.conditions || []), ...conditions];
    return this;
  }

  public withOptions(
    options:
      | IFieldOption[]
      | Observable<IFieldOption[]>
      | (() => Observable<IFieldOption[]>)
  ): FieldConfigBuilder {
    this.config.options = options;
    return this;
  }

  public withOptionConfig(optionConfig: IOptionConfig): FieldConfigBuilder {
    this.config.optionConfig = optionConfig;
    return this;
  }

  public withGrid(grid: IGridConfig): FieldConfigBuilder {
    this.config.grid = grid;
    return this;
  }

  public withAppearance(appearance: FieldAppearance): FieldConfigBuilder {
    this.config.appearance = appearance;
    return this;
  }

  public withSize(size: FieldSize): FieldConfigBuilder {
    this.config.size = size;
    return this;
  }

  public withLabelPosition(position: FieldLabelPosition): FieldConfigBuilder {
    this.config.labelPosition = position;
    return this;
  }

  public withCssClass(cssClass: string | string[]): FieldConfigBuilder {
    this.config.cssClass = cssClass;
    return this;
  }

  public withContainerClass(
    containerClass: string | string[]
  ): FieldConfigBuilder {
    this.config.containerClass = containerClass;
    return this;
  }

  public withLabelClass(labelClass: string | string[]): FieldConfigBuilder {
    this.config.labelClass = labelClass;
    return this;
  }

  public withErrorClass(errorClass: string | string[]): FieldConfigBuilder {
    this.config.errorClass = errorClass;
    return this;
  }

  public withPrefix(prefix: IFieldAddon): FieldConfigBuilder {
    this.config.prefix = prefix;
    return this;
  }

  public withSuffix(suffix: IFieldAddon): FieldConfigBuilder {
    this.config.suffix = suffix;
    return this;
  }

  public withMask(mask: string | IInputMask): FieldConfigBuilder {
    this.config.mask = mask;
    return this;
  }

  public withDebounceTime(debounceTime: number): FieldConfigBuilder {
    this.config.debounceTime = debounceTime;
    return this;
  }

  public withThrottleTime(throttleTime: number): FieldConfigBuilder {
    this.config.throttleTime = throttleTime;
    return this;
  }

  public withUpdateOn(
    updateOn: 'change' | 'blur' | 'submit'
  ): FieldConfigBuilder {
    this.config.updateOn = updateOn;
    return this;
  }

  public withAsyncDataSource(
    asyncDataSource: IAsyncDataSource
  ): FieldConfigBuilder {
    this.config.asyncDataSource = asyncDataSource;
    return this;
  }

  public withCustomComponent(customComponent: Type<any>): FieldConfigBuilder {
    this.config.customComponent = customComponent;
    return this;
  }

  public withCustomProperty(key: string, value: any): FieldConfigBuilder {
    this.config.customProperties = this.config.customProperties || {};
    this.config.customProperties[key] = value;
    return this;
  }

  public withCustomProperties(
    properties: Record<string, any>
  ): FieldConfigBuilder {
    this.config.customProperties = {
      ...(this.config.customProperties || {}),
      ...properties,
    };
    return this;
  }

  public withMetadata(key: string, value: any): FieldConfigBuilder {
    this.config.metadata = this.config.metadata || {};
    this.config.metadata[key] = value;
    return this;
  }

  public withMetadataObject(metadata: Record<string, any>): FieldConfigBuilder {
    this.config.metadata = { ...(this.config.metadata || {}), ...metadata };
    return this;
  }

  private addValidation(validation: IFieldValidation): void {
    this.config.validations = this.config.validations || [];
    this.config.validations.push(validation);
  }

  public build(): FieldConfig {
    return new FieldConfig(this.config);
  }

  public buildConfig(): IFieldConfig {
    return this.build().toJSON();
  }
}
